pipeline {
    agent any
    parameters {
        string(name: 'PROJECT_DESCRIPTION', defaultValue: '凭证模块', description: '请输入项目描述')
    }
    tools {
        maven '3.8.1'  // 确保在 Jenkins 全局工具配置中定义了这个名称的 Maven
        jdk 'jdk11'     // 确保在 Jenkins 全局工具配置中定义了这个名称的 JDK
    }
    stages {
//        stage('Checkout SCM') {
//            steps {
//                git url: 'https://e.coding.net/xy-tech-erp/meta/xy-erp-common.git',
//                        branch: 'master',
//                        credentialsId: 'c59ccd73-b244-41fa-9a50-ca8430f380f0' // Jenkins 中存储的 Git 凭据 ID
//            }
//        }

        stage('Build & Test') {
            steps {
                sh "mvn clean install -Dmaven.test.skip=true -s ${env.JENKINS_HOME}/settings.xml"
                // 根据实际情况调整 Maven 命令（如 install、verify 等）
                sh "mvn deploy -s ${env.JENKINS_HOME}/settings.xml"
            }
        }

        stage('Create Git Tag') {
            steps {
                script {
                    // 从 pom.xml 获取项目版本
                    def projectVersion = sh(
                            script: "mvn help:evaluate -Dexpression=project.version -q -DforceStdout -s ${env.JENKINS_HOME}/settings.xml",
                            returnStdout: true
                    ).trim()

                    // 项目版本放到环境变量
                    env.VERSION_NUMBER = projectVersion

                    // 组合标签名称（示例格式：v1.0.0）
                    def tagName = "v${projectVersion}"

                    // 创建并推送标签 (不能覆盖远程仓库的tag)
//                    withCredentials([usernamePassword(
//                            credentialsId: 'c59ccd73-b244-41fa-9a50-ca8430f380f0',
//                            usernameVariable: 'GIT_USER',
//                            passwordVariable: 'GIT_PASSWORD'
//                    )]) {
//                        sh """
//                            git config user.name "SeaOrange"
//                            git config user.email "<EMAIL>"
//                            git tag -d ${tagName}
//                            git tag -a ${tagName} -m "Automated tag by Jenkins build ${env.BUILD_NUMBER}"
//                            git push origin ${tagName}
//                        """
//                    }
                }
            }
        }
    }

    post {
        success {
            script {
                echo 'Build succeeded! Tag pushed to repository.'
                def GIT_PREVIOUS_SUCCESSFUL_COMMIT = "${env.GIT_PREVIOUS_SUCCESSFUL_COMMIT}"
                if (GIT_PREVIOUS_SUCCESSFUL_COMMIT == "null") {
                    sendToWechat('')
                } else {
                    def GIT_LOG = sh(
                            script: "git log ${GIT_PREVIOUS_SUCCESSFUL_COMMIT}..${env.GIT_COMMIT} --no-merges --pretty=format:'%s'",
                            returnStdout: true
                    )
                    sendToWechat(GIT_LOG)
                }
            }
        }
        failure {
            echo 'Build failed! See logs for details.'
            sendFailure()
        }
    }
}

// 推送企业微信
def sendToWechat(String GIT_LOG) {
    // \"content\":\"# 发版提醒 \\n 项目：<font color=\\\"info\\\">${env.JOB_NAME}</font> \\n 描述：<font color=\\\"comment\\\">${desc}</font> \\n 版本号：<font color=\\\"warning\\\">${versionNumber}</font> \\n 提交记录：\\n > ${GIT_LOG}\"

    def content = """
    # 发包提醒 \\n 项目：<font color=\\"info\\">${env.JOB_NAME}</font> \\n 描述：<font color=\\"comment\\">${params.PROJECT_DESCRIPTION}</font> \\n 版本号：<font color=\\"warning\\">${env.VERSION_NUMBER}</font> \\n 提交记录：\\n > ${GIT_LOG}
    """

    def message = """
    {
         "msgtype": "markdown",
         "markdown": {
             "content": "${content}"
         }
    }
    """
    sh(
            script: "curl -X POST -H 'Content-Type: application/json' -d '${message}' 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fc16e804-ca42-4305-a27f-fdf652fc81b2'",
            returnStdout: true
    )
}

// 推送发版记录
def sendToReleaseLog(String GIT_LOG, String code, String name, String desc) {
    def base64Str = sh(
            script: "echo -n '$GIT_LOG' | base64",
            returnStdout: true
    ).trim()
    // 去除换行
    def commitDesc = sh(
            script: "echo '$base64Str' | tr -d '\n'",
            returnStdout: true
    )
    // 发版记录
    def issueData = """
    {
        "customerCode": "${code}",
        "customerName": "${name}",
        "projectName": "${env.JOB_NAME}",
        "description": "${desc}",
        "versionCode": "${versionNumber}",
        "commitDesc": "${commitDesc}",
        "issueDesc": "",
        "issueDate": "2024-08-16 22:45:21",
        "messageJson": "",
    }
    """

    httpRequest(
            url: "http://issue-api.xyzlerp.com/issue-log/create",
            httpMode: 'POST',
            contentType: 'APPLICATION_JSON',
            requestBody: "${issueData}",
            quiet: true
    )
}

// 发包失败
def sendFailure() {
    def content = """
    # 发包失败 \\n 项目：<font color=\\"info\\">${env.JOB_NAME}</font> \\n 描述：<font color=\\"comment\\">${params.PROJECT_DESCRIPTION}</font>
    """

    def message = """
    {
         "msgtype": "markdown",
         "markdown": {
             "content": "${content}"
         }
    }
    """

    sh(
            script: "curl -X POST -H 'Content-Type: application/json' -d '${message}' 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fc16e804-ca42-4305-a27f-fdf652fc81b2'",
            returnStdout: true
    )
}