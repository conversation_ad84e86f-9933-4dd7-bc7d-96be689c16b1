package com.xunyue.proof;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.proof.entity.model.dto.ProofDTO;
import com.xunyue.proof.service.IProofService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@Slf4j
@SpringBootTest(classes = ProofApplication.class)
class ProofApplicationTest {

    @Autowired
    private IProofService proofService;


    @Test
    void contextLoads() {
        System.out.println("测试");
    }

    /**
     * 根据类型id批量查节点类型
//     * @param ids  节点id集合
     * @return
     */
    @Test
    void selectList() {
        ProofDTO proofDTO = new ProofDTO();
        proofDTO.setProofId("");
        //MPJLambdaWrapper<Proof> lambdaWrapper = JoinWrappers.lambda(Proof.class).selectAll().from(t -> proofService.buildSelectAllWrapper(new ProofDTO()));
        List<ProofDTO> proofDTOS = proofService.selectJoinList(ProofDTO.class, proofService.buildSelectAllWrapper(new ProofDTO()));
        System.out.println(proofDTOS);

    }

    @Test
    void  getOneById() {
        ProofDTO oneById = proofService.getOneById("7dc0c93a46e3a99bb33781507c63b94d");
        System.out.println(oneById);
    }

    @Test
    void findList() {
        List<ProofDTO> resultList = proofService.findList(new ProofDTO());
        System.out.println(resultList);
    }

    @Test
    void findPage() {
        IPage<ProofDTO> resultPage = proofService.findPage(new ProofDTO());
        System.out.println(resultPage);
    }

    @Test
    void testCopy() {
        ProofDTO proofDTO = new ProofDTO();
        proofDTO.setProofId("7dc0c93a46e3a99bb33781507c63b94d");
        proofDTO.setProofTenantId("2caf3cb6216111eea71b49e0880a97d9");

        proofService.save(proofDTO);

        log.info(JSON.toJSONString(proofDTO));

    }
}
