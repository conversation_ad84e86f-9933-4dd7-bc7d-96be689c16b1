package com.xunyue.proof;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(
//        scanBasePackages = {"com.xunyue.*", "xy.server.*"},
        exclude = {
                org.activiti.spring.boot.SecurityAutoConfiguration.class,
                org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class,
        }
)
//@MapperScan({"com.xunyue.*.mapper"})
//@SpringBootApplication
public class ProofApplication {

    public static void main(String[] args) {
        SpringApplication.run(ProofApplication.class, args);
    }
}
