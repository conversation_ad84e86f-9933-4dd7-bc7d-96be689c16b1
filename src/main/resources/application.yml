spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:postgresql://************:5432/xy_smart_node?useUnicode=true&characterEncoding=utf-8&useSSL=false&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=Asia/Shanghai&zeroDateTimeBehavior=CONVERT_TO_NULL&allowMultiQueries=true&useAffectedRows=true&charset=utf8mb4
    username: root
    password: 123456
    hikari:
      connection-timeout: 300000  # 连接超时时间 - 默认值：30秒。
      validation-timeout: 5000  # 连接被测试活动的最长时间 - 默认值：5秒。
      idle-timeout: 60000  # 连接池中允许闲置的最长时间 - 默认值：10分钟
      max-lifetime: 1800000  # 一个连接生命时长（毫秒），超时而没被使用则被释放 - 默认值：30分钟
      maximum-pool-size: 500  # 连接池中允许的最大连接数，包括闲置和使用中的连接 - 默认值：10
      minimum-idle: 30  # 连接池中允许的最小空闲连接数 - 默认值：10。
  redis:
    database: 9
    host: ************
    port: 6379

  activiti:
    database-type: postgres # 数据库类型
    #自动更新数据库结构
    # true：适用开发环境，默认值。activiti会对数据库中所有表进行更新操作。如果表不存在，则自动创建
    # false：适用生产环境。activiti在启动时，对比数据库表中保存的版本，如果没有表或者版本不匹配，将抛出异常
    # create_drop： 在activiti启动时创建表，在关闭时删除表（必须手动关闭引擎，才能删除表）
    # drop-create： 在activiti启动时删除原来的旧表，然后在创建新表（不需要手动关闭引擎）
    database-schema-update: true
    # activiti7与springboot整合后默认不创建历史表，需要手动开启
    db-history-used: true
    # 记录历史等级 可配置的历史级别有none, activity, audit, full
    # none：不保存任何的历史数据，因此，在流程执行过程中，这是最高效的。
    # activity：级别高于none，保存流程实例与流程行为，其他数据不保存。
    # audit：除activity级别会保存的数据外，还会保存全部的流程任务及其属性。
    # full：保存历史数据的最高级别，除了会保存audit级别的数据外，还会保存其他全部流程相关的细节数据，包括一些流程参数等。
    history-level: full
    # 是否自动检查resources下的processes目录的流程定义文件
    check-process-definitions: false
    # 关闭不自动添加部署数据 SpringAutoDeployment
    deployment-mode: never-fail
    # #开启异步true，定时任务
    async-executor-activate: false
    process-definition-location-prefix: classpath:/processes/ #流程定义文件存放目录
    process-definition-location-suffixes: #流程文件格式
      - .bpmn20.xml
      - .bpmn


mybatis-plus:
  type-aliases-package: com.xunyue.*
  mapper-locations: "classpath*:/mapper/*.xml"
  configuration:
    map-underscore-to-camel-case: true  # 开启下划线和驼峰
    cache-enabled: false   # 二级缓存
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_uuid
      update-strategy: not_null  # 更新策略：只更新非空字段
      logic-delete-field: is_deleted


# 租户中心地址
tenant-platform:
  #  path: http://************:8088
  path: http://127.0.0.1:8088
tenant:
  id: 2caf3cb6216111eea71b49e0880a97d9
  #  id: 6083cf926a717ef5045fd8e8a60ae516
  version: ${TENANT_VERSION:xy_mes}

# 忽略拦截的URL
ignore:
  uri:
    - /activiti/processDefinition/getProcessDefinitionDiagram
