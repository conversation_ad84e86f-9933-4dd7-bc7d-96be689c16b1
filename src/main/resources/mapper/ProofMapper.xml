<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunyue.proof.mapper.ProofMapper">

    <select id="getSerialNumber" resultType="java.lang.Integer">
        SELECT
            RIGHT( code, #{digit} ) code
        FROM
            meta_smart_node
        WHERE
            code LIKE concat(#{redisValue},'%')
        ORDER BY
            RIGHT ( code, #{digit} ) DESC
    </select>
</mapper>
