package com.xunyue.proof;

import com.xunyue.codegen.CodeGenerator;

/**
 * <AUTHOR>
 * @date 2024/12/26
 * @description
 **/
public class ProofCodeGenerator {

    public static void main(String[] args) {
        CodeGenerator codeGenerator = new CodeGenerator();

        // 设置包名
        codeGenerator.setPackName("com.xunyue.proof");
        // 设置是node
        codeGenerator.setIsNode(true);
        // 设置是prof
        codeGenerator.setIsProof(false);
        // 设置是否使用log
        codeGenerator.setIsNodeLog(false);
        // 设置作者
        codeGenerator.setAuthor("lizonglin");

        // 设置要生成的表名
        codeGenerator.setTables(new String[]{"xy_task"});

        // 生成
        codeGenerator.generate();
    }
}
