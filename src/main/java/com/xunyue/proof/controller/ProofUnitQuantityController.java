package com.xunyue.proof.controller;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import com.xunyue.proof.entity.ProofUnitQuantity;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.proof.service.IProofUnitQuantityService;
import com.xunyue.proof.entity.model.dto.ProofUnitQuantityDTO;

import java.util.List;


/**
 * @apiNote 单据单位数量关联表 controller
 * <AUTHOR>
 * @since 2025-01-15
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "单据单位数量关联表")
@RestController
@RequestMapping("/proof-unit-quantity")
@SystemClassLog(code = "ProofUnitQuantityController")
public class ProofUnitQuantityController {

    private final IProofUnitQuantityService service;

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<ProofUnitQuantityDTO> findList(@RequestBody @Validated ProofUnitQuantityDTO dto) {
        return service.findList(dto);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询")
    public IPage<ProofUnitQuantityDTO> findPage(@RequestBody @Validated ProofUnitQuantityDTO dto) {
        return service.findPage(dto);
    }

    @PostMapping("/saveOrUpdateBatch")
    @ApiOperation(value = "批量保存或更新单据单位数量关联表")
    @SystemMethodLog(type = "modify", description = "批量保存或更新单据单位数量关联表")
    public List<ProofUnitQuantityDTO> saveOrUpdateBatch(@RequestBody @Validated List<ProofUnitQuantityDTO> dtoList) {
        return service.saveOrUpdateBatch(dtoList);
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "保存或更新单据单位数量关联表")
    @SystemMethodLog(type = "modify", description = "保存或更新单据单位数量关联表")
    public ProofUnitQuantityDTO saveOrUpdate(@RequestBody @Validated ProofUnitQuantityDTO dto) {
        return service.saveOrUpdate(dto);
    }

    @PostMapping("/saveBatch")
    @ApiOperation(value = "批量保存单据单位数量关联表")
    @SystemMethodLog(type = "modify", description = "批量保存单据单位数量关联表")
    public List<ProofUnitQuantityDTO> saveBatch(@RequestBody @Validated List<ProofUnitQuantityDTO> dtoList) {
        return service.saveBatch(dtoList);
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存单据单位数量关联表")
    @SystemMethodLog(type = "modify", description = "保存单据单位数量关联表")
    public ProofUnitQuantityDTO save(@RequestBody @Validated ProofUnitQuantityDTO dto) {
        return service.save(dto);
    }

    @PostMapping("/updateBatch")
    @ApiOperation(value = "批量更新单据单位数量关联表")
    @SystemMethodLog(type = "modify", description = "批量更新单据单位数量关联表")
    public List<ProofUnitQuantityDTO> updateBatch(@RequestBody @Validated List<ProofUnitQuantityDTO> dtoList) {
        return service.updateBatch(dtoList);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新单据单位数量关联表")
    @SystemMethodLog(type = "modify", description = "更新单据单位数量关联表")
    public ProofUnitQuantityDTO update(@RequestBody @Validated ProofUnitQuantityDTO dto) {
        return service.update(dto);
    }

    @PostMapping("/remove/{id}")
    @ApiOperation(value = "删除单据单位数量关联表")
    @SystemMethodLog(type = "delete", description = "删除单据单位数量关联表")
    public Boolean remove(@PathVariable("id") String id) {
        return service.removeById(id);
    }

    @PostMapping("/removeBatch")
    @ApiOperation(value = "批量删除单据单位数量关联表")
    @SystemMethodLog(type = "delete", description = "批量删除单据单位数量关联表")
    public Boolean removeBatch(@RequestBody @Validated List<String> ids) {
        return service.removeBatch(ids);
    }
}
