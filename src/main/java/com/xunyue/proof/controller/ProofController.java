package com.xunyue.proof.controller;

import com.xunyue.config.aspect.SystemMethodLog;
import com.xunyue.node.common.enums.TypeEnum;
import com.xunyue.node.entity.Link;
import com.xunyue.node.entity.model.dto.LinkDTO;
import com.xunyue.node.service.ILinkService;
import com.xunyue.proof.entity.Proof;
import com.xunyue.proof.entity.model.dto.StateUpdateDTO;
import com.xunyue.proof.entity.model.dto.UpdateStorageStateAndQuantityDTO;
import com.xunyue.proof.service.IProofService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2024-12-12 9:50
 * @apiNote proof 控制层
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "proof")
@RestController
@RequestMapping("/proof")
public class ProofController {

    private final IProofService service;

    private final ILinkService linkService;

    @PostMapping("/linkFindList")
    @ApiOperation(value = "批量查询link")
    @SystemMethodLog(type = "modify", description = "批量查询link")
    public List<LinkDTO> saveLinkBatch(@RequestBody LinkDTO linkDTO) {
        return linkService.findList(linkDTO);
    }

    @PostMapping("/saveLinkBatch")
    @ApiOperation(value = "批量保存link")
    @SystemMethodLog(type = "modify", description = "批量保存link")
    public List<LinkDTO> saveLinkBatch(@RequestBody @Validated List<LinkDTO> linkDTOS) {
        return service.saveLinkBatch(linkDTOS);
    }

    @PostMapping("/saveLink")
    @ApiOperation(value = "单条保存link")
    @SystemMethodLog(type = "modify", description = "单条保存link")
    public LinkDTO saveLink(@RequestParam @NotBlank String source, @RequestParam @NotBlank String target, @RequestParam @NotNull TypeEnum typeEnum) {
        return service.saveLink(source, target, typeEnum);
    }

    @PostMapping("/saveIncludeLinkBatch")
    @ApiOperation(value = "批量保存包含类型的link")
    @SystemMethodLog(type = "modify", description = "批量保存包含类型的link")
    public List<LinkDTO> saveIncludeLinkBatch(@RequestBody @Validated List<LinkDTO> linkDTOS) {
        return service.saveIncludeLinkBatch(linkDTOS);
    }

    @PostMapping("/saveIncludeLink")
    @ApiOperation(value = "单条保存包含类型的link")
    @SystemMethodLog(type = "modify", description = "单条保存包含类型的link")
    public LinkDTO saveIncludeLink(@RequestParam @NotBlank String source, @RequestParam @NotBlank String target) {
        return service.saveIncludeLink(source, target);
    }

    @PostMapping("/saveSourceLinkBatch")
    @ApiOperation(value = "批量保存来源类型的link")
    @SystemMethodLog(type = "modify", description = "批量保存来源类型的link")
    public List<LinkDTO> saveSourceLinkBatch(@RequestBody @Validated List<LinkDTO> linkDTOS) {
        return service.saveSourceLinkBatch(linkDTOS);
    }

    @PostMapping("/saveSourceLink")
    @ApiOperation(value = "单条保存来源类型的link")
    @SystemMethodLog(type = "modify", description = "单条保存来源类型的link")
    public LinkDTO saveSourceLink(@RequestParam @NotBlank String source, @RequestParam @NotBlank String target) {
        return service.saveSourceLink(source, target);
    }

    @PostMapping("/saveSequenceLinkBatch")
    @ApiOperation(value = "批量保存顺序类型的link")
    @SystemMethodLog(type = "modify", description = "批量保存顺序类型的link")
    public List<LinkDTO> saveSequenceLinkBatch(@RequestBody @Validated List<LinkDTO> linkDTOS) {
        return service.saveSequenceLinkBatch(linkDTOS);
    }

    @PostMapping("/saveSequenceLink")
    @ApiOperation(value = "单条保存顺序类型的link")
    @SystemMethodLog(type = "modify", description = "单条保存顺序类型的link")
    public LinkDTO saveSequenceLink(@RequestParam @NotBlank String source, @RequestParam @NotBlank String target) {
        return service.saveSequenceLink(source, target);
    }

    @PostMapping("/saveMessageLinkBatch")
    @ApiOperation(value = "批量保存消息类型的link")
    @SystemMethodLog(type = "modify", description = "批量保存消息类型的link")
    public List<LinkDTO> saveMessageLinkBatch(@RequestBody @Validated List<LinkDTO> linkDTOS) {
        return service.saveMessageLinkBatch(linkDTOS);
    }

    @PostMapping("/saveMessageLink")
    @ApiOperation(value = "单条保存消息类型的link")
    @SystemMethodLog(type = "modify", description = "单条保存消息类型的link")
    public LinkDTO saveMessageLink(@RequestParam @NotBlank String source, @RequestParam @NotBlank String target) {
        return service.saveMessageLink(source, target);
    }

    @PostMapping("/removeLinkBySources")
    @ApiOperation(value = "根据sources删除Link")
    @SystemMethodLog(type = "delete", description = "根据sources删除Link")
    public boolean removeLinkBySources(@RequestBody @NotEmpty Collection<String> sources, @RequestParam @NotNull TypeEnum typeEnum) {
        return service.removeLinkBySources(sources, typeEnum);
    }

    @PostMapping("/removeIncludeLinkBySources")
    @ApiOperation(value = "根据sources删除包含类型的Link")
    @SystemMethodLog(type = "delete", description = "根据sources删除包含类型的Link")
    public boolean removeIncludeLinkBySources(@RequestBody @NotEmpty Collection<String> sources) {
        return service.removeIncludeLinkBySources(sources);
    }

    @PostMapping("/removeIncludeLinkBySource")
    @ApiOperation(value = "根据source删除包含类型的Link")
    @SystemMethodLog(type = "delete", description = "根据source删除包含类型的Link")
    public boolean removeIncludeLinkBySource(@RequestParam @NotBlank String source) {
        return service.removeIncludeLinkBySource(source);
    }

    @PostMapping("/removeSourceLinkBySources")
    @ApiOperation(value = "根据sources删除来源类型的Link")
    @SystemMethodLog(type = "delete", description = "根据sources删除来源类型的Link")
    public boolean removeSourceLinkBySources(@RequestBody @NotEmpty Collection<String> sources) {
        return service.removeSourceLinkBySources(sources);
    }

    @PostMapping("/removeSourceLinkBySource")
    @ApiOperation(value = "根据source删除来源类型的Link")
    @SystemMethodLog(type = "delete", description = "根据source删除来源类型的Link")
    public boolean removeSourceLinkBySource(@RequestParam @NotBlank String source) {
        return service.removeSourceLinkBySource(source);
    }

    @PostMapping("/removeSequenceLinkBySources")
    @ApiOperation(value = "根据sources删除顺序类型的Link")
    @SystemMethodLog(type = "delete", description = "根据sources删除顺序类型的Link")
    public boolean removeSequenceLinkBySources(@RequestBody @NotEmpty Collection<String> sources) {
        return service.removeSequenceLinkBySources(sources);
    }

    @PostMapping("/removeSequenceLinkBySource")
    @ApiOperation(value = "根据source删除顺序类型的Link")
    @SystemMethodLog(type = "delete", description = "根据source删除顺序类型的Link")
    public boolean removeSequenceLinkBySource(@RequestParam @NotBlank String source) {
        return service.removeSequenceLinkBySource(source);
    }

    @PostMapping("/removeMessageLinkBySources")
    @ApiOperation(value = "根据sources删除消息类型的Link")
    @SystemMethodLog(type = "delete", description = "根据sources删除消息类型的Link")
    public boolean removeMessageLinkBySources(@RequestBody @NotEmpty Collection<String> sources) {
        return service.removeMessageLinkBySources(sources);
    }

    @PostMapping("/removeMessageLinkBySource")
    @ApiOperation(value = "根据source删除消息类型的Link")
    @SystemMethodLog(type = "delete", description = "根据source删除消息类型的Link")
    public boolean removeMessageLinkBySource(@RequestParam @NotBlank String source) {
        return service.removeMessageLinkBySource(source);
    }

    @PostMapping("/removeLinkByTargets")
    @ApiOperation(value = "根据targets删除Link")
    @SystemMethodLog(type = "delete", description = "根据targets删除Link")
    public boolean removeLinkByTargets(@RequestBody @NotEmpty Collection<String> targets, @RequestParam @NotNull TypeEnum typeEnum) {
        return service.removeLinkByTargets(targets, typeEnum);
    }

    @PostMapping("/removeIncludeLinkByTargets")
    @ApiOperation(value = "根据targets删除包含类型的Link")
    @SystemMethodLog(type = "delete", description = "根据targets删除包含类型的Link")
    public boolean removeIncludeLinkByTargets(@RequestBody @NotEmpty Collection<String> targets) {
        return service.removeIncludeLinkByTargets(targets);
    }

    @PostMapping("/removeIncludeLinkByTarget")
    @ApiOperation(value = "根据target删除包含类型的Link")
    @SystemMethodLog(type = "delete", description = "根据target删除包含类型的Link")
    public boolean removeIncludeLinkByTarget(@RequestParam @NotBlank String target) {
        return service.removeIncludeLinkByTarget(target);
    }

    @PostMapping("/removeSourceLinkByTargets")
    @ApiOperation(value = "根据targets删除来源类型的Link")
    @SystemMethodLog(type = "delete", description = "根据targets删除来源类型的Link")
    public boolean removeSourceLinkByTargets(@RequestBody @NotEmpty Collection<String> targets) {
        return service.removeSourceLinkByTargets(targets);
    }

    @PostMapping("/removeSourceLinkByTarget")
    @ApiOperation(value = "根据target删除来源类型的Link")
    @SystemMethodLog(type = "delete", description = "根据target删除来源类型的Link")
    public boolean removeSourceLinkByTarget(@RequestParam @NotBlank String target) {
        return service.removeSourceLinkByTarget(target);
    }

    @PostMapping("/removeSequenceLinkByTargets")
    @ApiOperation(value = "根据targets删除顺序类型的Link")
    @SystemMethodLog(type = "delete", description = "根据targets删除顺序类型的Link")
    public boolean removeSequenceLinkByTargets(@RequestBody @NotEmpty Collection<String> targets) {
        return service.removeSequenceLinkByTargets(targets);
    }

    @PostMapping("/removeSequenceLinkByTarget")
    @ApiOperation(value = "根据target删除顺序类型的Link")
    @SystemMethodLog(type = "delete", description = "根据target删除顺序类型的Link")
    public boolean removeSequenceLinkByTarget(@RequestParam @NotBlank String target) {
        return service.removeSequenceLinkByTarget(target);
    }

    @PostMapping("/removeMessageLinkByTargets")
    @ApiOperation(value = "根据targets删除消息类型的Link")
    @SystemMethodLog(type = "delete", description = "根据targets删除消息类型的Link")
    public boolean removeMessageLinkByTargets(@RequestBody @NotEmpty Collection<String> targets) {
        return service.removeMessageLinkByTargets(targets);
    }

    @PostMapping("/removeMessageLinkByTarget")
    @ApiOperation(value = "根据target删除消息类型的Link")
    @SystemMethodLog(type = "delete", description = "根据target删除消息类型的Link")
    public boolean removeMessageLinkByTarget(@RequestParam @NotBlank String target) {
        return service.removeMessageLinkByTarget(target);
    }


    @PostMapping("/updateStateAndQuantity")
    @ApiOperation(value = "proof完成数量和状态反写")
    @SystemMethodLog(type = "modify", description = "proof完成数量和状态反写")
    public List<Proof> updateStateAndQuantity(@RequestBody @Validated List<StateUpdateDTO> dtos) {
        return service.updateStateAndQuantity(dtos);
    }

}
