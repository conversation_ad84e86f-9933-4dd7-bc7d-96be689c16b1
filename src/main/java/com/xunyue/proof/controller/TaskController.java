package com.xunyue.proof.controller;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import com.xunyue.proof.entity.Task;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.xunyue.config.aspect.SystemClassLog;
import com.xunyue.config.aspect.SystemMethodLog;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xunyue.proof.service.ITaskService;
import com.xunyue.proof.entity.model.dto.TaskDTO;

import java.util.List;


/**
 * @apiNote  controller
 * <AUTHOR>
 * @since 2025-01-23
 */
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@Api(tags = "生产任务")
@RestController
@RequestMapping("/task")
@SystemClassLog(code = "TaskController")
public class TaskController {

    private final ITaskService service;

    @PostMapping("/findList")
    @ApiOperation(value = "列表查询")
    @SystemMethodLog(type = "query", description = "列表查询")
    public List<TaskDTO> findList(@RequestBody @Validated TaskDTO dto) {
        return service.findList(dto);
    }

    @PostMapping("/findPage")
    @ApiOperation(value = "分页查询")
    @SystemMethodLog(type = "query", description = "分页查询")
    public IPage<TaskDTO> findPage(@RequestBody @Validated TaskDTO dto) {
        return service.findPage(dto);
    }

    @PostMapping("/saveOrUpdateBatch")
    @ApiOperation(value = "批量保存或更新")
    @SystemMethodLog(type = "modify", description = "批量保存或更新")
    public List<TaskDTO> saveOrUpdateBatch(@RequestBody @Validated List<TaskDTO> dtoList) {
        return service.saveOrUpdateBatch(dtoList);
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation(value = "保存或更新")
    @SystemMethodLog(type = "modify", description = "保存或更新")
    public TaskDTO saveOrUpdate(@RequestBody @Validated TaskDTO dto) {
        return service.saveOrUpdate(dto);
    }

    @PostMapping("/saveBatch")
    @ApiOperation(value = "批量保存")
    @SystemMethodLog(type = "modify", description = "批量保存")
    public List<TaskDTO> saveBatch(@RequestBody @Validated List<TaskDTO> dtoList) {
        return service.saveBatch(dtoList);
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存")
    @SystemMethodLog(type = "modify", description = "保存")
    public TaskDTO save(@RequestBody @Validated TaskDTO dto) {
        return service.save(dto);
    }

    @PostMapping("/updateBatch")
    @ApiOperation(value = "批量更新")
    @SystemMethodLog(type = "modify", description = "批量更新")
    public List<TaskDTO> updateBatch(@RequestBody @Validated List<TaskDTO> dtoList) {
        return service.updateBatch(dtoList);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新")
    @SystemMethodLog(type = "modify", description = "更新")
    public TaskDTO update(@RequestBody @Validated TaskDTO dto) {
        return service.update(dto);
    }

    @PostMapping("/remove/{id}")
    @ApiOperation(value = "删除")
    @SystemMethodLog(type = "delete", description = "删除")
    public Boolean remove(@PathVariable("id") String id) {
        return service.removeById(id);
    }

    @PostMapping("/removeBatch")
    @ApiOperation(value = "批量删除")
    @SystemMethodLog(type = "delete", description = "批量删除")
    public Boolean removeBatch(@RequestBody @Validated List<String> ids) {
        return service.removeBatch(ids);
    }
}
