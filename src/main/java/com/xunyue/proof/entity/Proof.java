package com.xunyue.proof.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 元数据 类型和节点关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Data
@Accessors(chain = true)
@TableName(value = "meta_smart_proof")
public class Proof implements Serializable {

    /**
     * pk
     */
    @TableId
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 任务的负责人 后面搬到task
     */
    @ApiModelProperty(value = "源")
    @Deprecated
    private String source;

    /**
     * 任务的指派人 后面搬到task
     */
    @ApiModelProperty(value = "目标")
    @Deprecated
    private String target;

    /**
     * 任务的指派人开始时间 后面搬到task
     */
    @ApiModelProperty(value = "开始时间")
    @Deprecated
    private Date beginTime;

    /**
     * 任务的指派人结束时间 后面搬到task
     */
    @ApiModelProperty(value = "结束时间")
    @Deprecated
    private Date endTime;

    /**
     * 打印次数
     */
    @ApiModelProperty(value = "打印次数")
    private Long printTimes;

    /**
     * 交货日期
     */
    @ApiModelProperty(value = "交货日期")
    private Date deliveryDate;

    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    private String sourceType;

    /**
     * 单据类型（一个常量值，与node里的defaultTypeId不一样，推荐使用字典）
     */
    @ApiModelProperty(value = "单据类型（一个常量值，与node里的defaultTypeId不一样，推荐使用字典）")
    private String receiptType;

    /**
     * 单据日期
     */
    @ApiModelProperty(value = "单据日期")
    private Date receiptDate;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer priority;

    /**
     * fsc声明
     */
    @ApiModelProperty(value = "FSC声明")
    private String fscType;

    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private String materialId;

    /**
     * 单位id
     */
    @ApiModelProperty(value = "单位id")
    private String unitCode;

    /**
     * 任务数量
     */
    @TableField("quantity")
    @ApiModelProperty(value = "任务数量")
    private BigDecimal quantity;

    /**
     * 任务完成数量
     */
    @TableField("complete_quantity")
    @ApiModelProperty(value = "完成数量")
    private BigDecimal completeQuantity;
    /**
     * 本单据状态
     */
    @TableField("complete_status")
    @ApiModelProperty(value = "完成状态")
    private String  completeStatus;

    /**
     * 设备id
     */
    @TableField("equipment_id")
    @ApiModelProperty(value = "设备id")
    private String equipmentId;

    /**
     * 可用日程id
     */
    @TableField("available_schedule_id")
    private String availableScheduleId;

    /**
     * 关联任务id
     */
    @TableField("related_tasks_id")
    private String relatedTasksId;

    /**
     * 工序类型_id
     */
    @TableField("production_processes_type_id")
    private String productionProcessesTypeId;

    /**
     * 生产班次_id
     */
    @TableField("production_shift_id")
    private String productionShiftId;


    /**
     * 计划产出数量
     */
    @TableField("planned_output_quantity")
    private BigDecimal plannedOutputQuantity;

    /**
     * 计划需时(小时)
     */
    @TableField("planned_time_required")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private BigDecimal plannedTimeRequired;


    /**
     * 最后需时(小时)
     */
    @TableField("end_time_required")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private BigDecimal endTimeRequired;

    /**
     * 实际需时(小时)
     */
    @TableField("actual_time_required")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private BigDecimal actualTimeRequired;

    /**
     * 是否手动修改计划生产时间
     */
    @TableField("is_manual_modification_date")
    private Boolean isManualModificationDate;

    /**
     * 是否叶子节点
     */
    @TableField("is_leaf")
    private Boolean isLeaf;


}
