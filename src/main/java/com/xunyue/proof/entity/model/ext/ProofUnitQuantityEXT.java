package com.xunyue.proof.entity.model.ext;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import com.xunyue.processor.anno.Proxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.List;

/**
* <p>
* 单据单位数量关联表Ext
* </p>
*
* <AUTHOR>
* @since 2025-01-15
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ProofUnitQuantityExt对象", description = "单据单位数量关联表")
public class ProofUnitQuantityEXT {

    @ApiModelProperty(value = "PK列表")
    private List<String> ids;

    @ApiModelProperty(value = "租户id列表")
    private List<String> tenantIds;

    @ApiModelProperty(value = "单位id列表")
    private List<String> unitIds;

}
