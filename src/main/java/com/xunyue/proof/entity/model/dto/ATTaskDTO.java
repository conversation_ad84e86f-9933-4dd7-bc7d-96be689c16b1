package com.xunyue.proof.entity.model.dto;

import com.xunyue.base.AbstractDTO;
import com.xunyue.node.entity.Node;
import com.xunyue.node.entity.model.ext.NodeEXT;
import com.xunyue.processor.anno.Proxy;
import com.xunyue.proof.entity.Proof;
import com.xunyue.proof.entity.Task;
import com.xunyue.proof.entity.model.ext.ProofEXT;
import com.xunyue.proof.entity.model.ext.TaskEXT;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
* <p>
* DTO
* </p>
*
* <AUTHOR>
* @since 2025-01-23
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "TaskDTO对象", description = "")
public class ATTaskDTO extends AbstractDTO {

    @ApiModelProperty(value = "实体")
    @Proxy
    private Task task = new Task();

    @ApiModelProperty(value = "实体扩展字段")
    @Proxy
    private TaskEXT taskEXT = new TaskEXT();

    
}
