package com.xunyue.proof.entity.model.ext;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2025-01-14 14:49
 * @apiNote
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProofEXT {

    @ApiModelProperty(value = "PK列表")
    private List<String> ids;

    @ApiModelProperty(value = "租户id列表")
    private List<String> tenantIds;

    @ApiModelProperty(value = "物料id列表")
    private List<String> materialIds;

    @ApiModelProperty(value = "关联任务id列表")
    private List<String> relatedTasksIds;

    @ApiModelProperty(value = "可用日程id列表")
    private List<String> availableScheduleIds;

    @ApiModelProperty(value = "生产班次_id列表")
    private List<String> productionShiftIds;

    @ApiModelProperty(value = "设备_id列表")
    private List<String> equipmentIds;

    @ApiModelProperty(value = "工序类型_id列表")
    private List<String> productionProcessesTypeIds;
}
