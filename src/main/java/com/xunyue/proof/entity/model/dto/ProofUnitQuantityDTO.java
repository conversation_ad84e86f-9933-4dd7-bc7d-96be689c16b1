package com.xunyue.proof.entity.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;

import com.xunyue.node.entity.model.ext.NodeEXT;
import com.xunyue.proof.entity.Proof;
import com.xunyue.proof.entity.model.ext.ProofEXT;
import com.xunyue.proof.entity.model.ext.ProofUnitQuantityEXT;
import com.xunyue.processor.anno.Proxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import com.xunyue.proof.entity.ProofUnitQuantity;

import com.xunyue.node.entity.Node;
import com.xunyue.base.AbstractDTO;

/**
* <p>
* 单据单位数量关联表DTO
* </p>
*
* <AUTHOR>
* @since 2025-01-15
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "ProofUnitQuantityDTO对象", description = "单据单位数量关联表")
public class ProofUnitQuantityDTO extends AbstractDTO {

    @ApiModelProperty(value = "单据单位数量关联表实体")
    @Proxy
    private ProofUnitQuantity proofUnitQuantity = new ProofUnitQuantity();

    @ApiModelProperty(value = "单据单位数量关联表实体扩展字段")
    @Proxy
    private ProofUnitQuantityEXT proofUnitQuantityEXT = new ProofUnitQuantityEXT();

    @ApiModelProperty(value = "node")
    @Proxy
    private Node node = new Node();

    @ApiModelProperty(value = "node实体扩展字段")
    @Proxy
    private NodeEXT nodeEXT = new NodeEXT();

    @ApiModelProperty(value = "proof")
    @Proxy
    private Proof proof = new Proof();

    @ApiModelProperty(value = "proof实体扩展字段")
    @Proxy
    private ProofEXT proofEXT = new ProofEXT();
    
}
