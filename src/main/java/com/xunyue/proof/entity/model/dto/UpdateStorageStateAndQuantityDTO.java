package com.xunyue.proof.entity.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class UpdateStorageStateAndQuantityDTO {

    @ApiModelProperty(value = "id")
    @NotBlank(message = "主键id不能传空")
    private String id;

    @ApiModelProperty(value = "状态")
    private String state;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
}
