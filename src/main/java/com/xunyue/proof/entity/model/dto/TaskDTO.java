package com.xunyue.proof.entity.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.xunyue.proof.entity.model.ext.TaskEXT;
import com.xunyue.processor.anno.Proxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xunyue.proof.entity.Task;

import com.xunyue.proof.entity.Proof;
import com.xunyue.proof.entity.model.ext.ProofEXT;
import com.xunyue.node.entity.Node;
import com.xunyue.node.entity.model.ext.NodeEXT;
import com.xunyue.base.AbstractDTO;

/**
* <p>
* DTO
* </p>
*
* <AUTHOR>
* @since 2025-01-23
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "TaskDTO对象", description = "")
public class TaskDTO extends AbstractDTO {

    @ApiModelProperty(value = "实体")
    @Proxy
    private Task task = new Task();

    @ApiModelProperty(value = "实体扩展字段")
    @Proxy
    private TaskEXT taskEXT = new TaskEXT();

    @ApiModelProperty(value = "proof")
    @Proxy
    private Proof proof = new Proof();

    @ApiModelProperty(value = "proof实体扩展字段")
    @Proxy
    private ProofEXT proofEXT = new ProofEXT();

    @ApiModelProperty(value = "node")
    @Proxy
    private Node node = new Node();

    @ApiModelProperty(value = "node实体扩展字段")
    @Proxy
    private NodeEXT nodeEXT = new NodeEXT();
    
}
