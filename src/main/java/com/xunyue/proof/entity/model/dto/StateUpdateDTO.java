package com.xunyue.proof.entity.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * @Title: StateUpdateDTO
 * <AUTHOR>
 * @Package com.xunyue.proof.entity.model.dto
 * @Date 2025/2/13 9:30
 * @description: proo数量和状态反写入参
 */
@Data
@NoArgsConstructor
public class StateUpdateDTO {
    @ApiModelProperty(value = "proofId")
    @NotBlank(message = "主键id不能传空")
    private String proofId;

    @ApiModelProperty(value = "完成状态")
    private String state;

    @ApiModelProperty(value = "完成数量")
    private BigDecimal quantity;
}
