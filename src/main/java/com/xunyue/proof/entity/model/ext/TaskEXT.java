package com.xunyue.proof.entity.model.ext;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.xunyue.processor.anno.Proxy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.List;

/**
* <p>
* Ext
* </p>
*
* <AUTHOR>
* @since 2025-01-23
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "TaskExt对象", description = "")
public class TaskEXT {

    @ApiModelProperty(value = "列表")
    private List<String> ids;

}
