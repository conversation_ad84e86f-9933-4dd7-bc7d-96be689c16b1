package com.xunyue.proof.entity.model.dto;

import com.xunyue.base.AbstractDTO;
import com.xunyue.node.entity.Node;
import com.xunyue.node.entity.model.ext.NodeEXT;
import com.xunyue.processor.anno.Proxy;
import com.xunyue.proof.entity.Proof;
import com.xunyue.proof.entity.model.ext.ProofEXT;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class ProofDTO extends AbstractDTO {

    @Proxy
    private Proof proof = new Proof();

    @Proxy
    private ProofEXT proofEXT = new ProofEXT();

    @Proxy
    private Node node = new Node();

    @Proxy
    private NodeEXT nodeEXT = new NodeEXT();


    public void setProofTenantId(String tenantId) {
        this.proof.setTenantId(tenantId);
        this.node.setIsolation(tenantId);
    }

    public void setProofTenantIds(List<String> tenantIds) {
        this.proofEXT.setTenantIds(tenantIds);
        this.nodeEXT.setIsolations(tenantIds);
    }
}
