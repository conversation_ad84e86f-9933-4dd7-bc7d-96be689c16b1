package com.xunyue.proof.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.FieldFill;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
                
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableLogic;

/**
 * <p>
 * 单据单位数量关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "xy_proof_unit_qua")
public class ProofUnitQuantity implements Serializable{

    private static final long serialVersionUID=1L;

    /**
     * PK
     */
    @TableId("id")
    @ApiModelProperty(value = "PK")
    private String id;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 单位id
     */
    @TableField("unit_id")
    @ApiModelProperty(value = "单位id")
    private String unitId;

    /**
     * 数量
     */
    @TableField("quantity")
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

}
