package com.xunyue.proof.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
                    
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableLogic;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "xy_task")
public class Task implements Serializable{

    private static final long serialVersionUID=1L;

    @TableId("id")
    @ApiModelProperty(value = "")
    private String id;

//    /**
//     * 任务类型
//     */
//    @TableField("type")
//    @ApiModelProperty(value = "任务类型")
//    private String type;
//
//    /**
//     * 任务状态
//     */
//    @TableField("status")
//    @ApiModelProperty(value = "任务状态")
//    private String status;
//
//    /**
//     * 任务截止日期
//     */
//    @TableField("deadline")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    @ApiModelProperty(value = "任务截止日期")
//    private LocalDateTime deadline;

}
