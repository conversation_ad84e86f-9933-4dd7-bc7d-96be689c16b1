package com.xunyue.proof.mapper;

import com.xunyue.base.XYBaseMapper;
import com.xunyue.proof.entity.Proof;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 元数据 凭证 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Mapper
public interface ProofMapper extends XYBaseMapper<Proof> {

    /**
     * 获取流水号
     * @param redisValue
     * @param digit
     * @return
     */
    Integer getSerialNumber(@Param("redisValue") String redisValue, @Param("digit") Integer digit);
}
