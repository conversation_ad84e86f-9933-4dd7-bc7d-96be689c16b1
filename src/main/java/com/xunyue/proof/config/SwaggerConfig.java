package com.xunyue.proof.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration(value = "SwaggerProofConfig")
@ConditionalOnProperty(name = "swagger.enable", havingValue = "true", matchIfMissing = true)
@EnableSwagger2
public class SwaggerConfig {

    @Bean(value = "proofApi")
    public Docket api() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("xy-erp-proof")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.xunyue.proof.controller"))
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("proof")
                .description("proof模块")
                .version("1.0")
                .build();
    }
}