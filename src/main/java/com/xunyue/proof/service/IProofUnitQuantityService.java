package com.xunyue.proof.service;

import com.xunyue.proof.entity.ProofUnitQuantity;
import com.xunyue.base.XYBaseService;
import com.xunyue.proof.entity.model.dto.ProofUnitQuantityDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.*;
import cn.hutool.core.collection.CollectionUtil;

/**
 * @apiNote 单据单位数量关联表 服务类
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface IProofUnitQuantityService extends XYBaseService<ProofUnitQuantity , ProofUnitQuantityDTO> {

    /**
     * 批量保存或更新单据单位数量关联表
     * @param dtoList 单据单位数量关联表信息的DTO对象列表，包含需要保存或更新的数据
     * @return 保存或更新后的单据单位数量关联表信息DTO对象列表，如果保存或更新失败则返回一个空数组
     */
    List<ProofUnitQuantityDTO> saveOrUpdateBatch(List<ProofUnitQuantityDTO> dtoList);

    /**
     * 保存或更新单据单位数量关联表
     *
     * @param dto 单据单位数量关联表信息的DTO对象，包含需要保存或更新的数据
     * @return 保存或更新后的单据单位数量关联表信息DTO对象，如果无结果则返回null
     */
    default ProofUnitQuantityDTO saveOrUpdate(ProofUnitQuantityDTO dto) {
        List<ProofUnitQuantityDTO> resultList = this.saveOrUpdateBatch(Collections.singletonList(dto));
        return CollectionUtil.isEmpty(resultList) ? null : resultList.get(0);
    }

    /**
     * 批量保存单据单位数量关联表
     * @param dtoList 单据单位数量关联表信息的DTO对象列表，包含需要保存的数据
     * @return 保存后的单据单位数量关联表信息DTO对象列表，如果保存失败则返回一个空数组
     */
    List<ProofUnitQuantityDTO> saveBatch(List<ProofUnitQuantityDTO> dtoList);

    /**
     * 保存单据单位数量关联表
     *
     * @param dto 单据单位数量关联表DTO对象，包含单据单位数量关联表的相关信息
     * @return 保存后的单据单位数量关联表DTO对象，如果保存失败则返回null
     */
    default ProofUnitQuantityDTO save(ProofUnitQuantityDTO dto) {
        List<ProofUnitQuantityDTO> resultList = this.saveBatch(Collections.singletonList(dto));
        return CollectionUtil.isEmpty(resultList) ? null : resultList.get(0);
    }

    /**
     * 更新单据单位数量关联表
     * @param dtoList 单据单位数量关联表信息的DTO对象列表，包含需要更新的数据
     * @return 更新后的单据单位数量关联表信息DTO对象列表，如果更新失败则返回一个空数组
     */
    List<ProofUnitQuantityDTO> updateBatch(List<ProofUnitQuantityDTO> dtoList);

    /**
     * 更新单据单位数量关联表
     *
     * @param dto 单据单位数量关联表dto
     * @return 更新后的单据单位数量关联表DTO对象如果更新操作失败或没有返回结果，则返回null
     */
    default ProofUnitQuantityDTO update(ProofUnitQuantityDTO dto) {
        List<ProofUnitQuantityDTO> resultList = this.updateBatch(Collections.singletonList(dto));
        return CollectionUtil.isEmpty(resultList) ? null : resultList.get(0);
    }

   /**
    * 根据id删除
    * @param id id
    * @return 删除结果
    */
    default Boolean removeById(String id){
        return this.removeBatch(Collections.singletonList(id));
    };

    /**
     * 批量删除
     * @param ids id集合
     * @return 删除结果
     */
    Boolean removeBatch(Collection<String> ids);

}
