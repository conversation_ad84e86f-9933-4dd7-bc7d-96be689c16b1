package com.xunyue.proof.service;

import com.xunyue.base.XYBaseService;
import com.xunyue.node.common.enums.TypeEnum;
import com.xunyue.node.entity.Link;
import com.xunyue.node.entity.model.dto.LinkDTO;
import com.xunyue.proof.entity.Proof;
import com.xunyue.proof.entity.model.dto.ProofDTO;
import com.xunyue.proof.entity.model.dto.StateUpdateDTO;
import com.xunyue.proof.entity.model.dto.UpdateStorageStateAndQuantityDTO;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @apiNote 元数据 凭证 服务类
 * @since 2024-11-26
 */
public interface IProofService extends XYBaseService<Proof, ProofDTO> {


    /**
     * 批量保存或更新
     * @param proofDTOS
     * @param isStartProcess 是否启动审核流
     * @return
     */
    boolean saveOrUpdateBatch(@NotEmpty List<ProofDTO> proofDTOS, boolean isStartProcess);

    /**
     * 批量保存或更新（默认不启动流程）
     * @param proofDTOS
     * @return
     */
    default boolean saveOrUpdateBatch(List<ProofDTO> proofDTOS) {
        return this.saveOrUpdateBatch(proofDTOS, false);
    }

    /**
     * 单条保存或更新
     * @param ProofDTO
     * @param isStartProcess 是否启动审核流
     * @return
     */
    default boolean saveOrUpdate(ProofDTO ProofDTO, boolean isStartProcess) {
        return this.saveOrUpdateBatch(Arrays.asList(ProofDTO), isStartProcess);
    }

    /**
     * 单条保存或更新（默认不启动流程）
     * @param ProofDTO
     * @return
     */
    default boolean saveOrUpdate(ProofDTO ProofDTO) {
        return this.saveOrUpdate(ProofDTO, false);
    }

    /**
     * 批量插入
     * @param proofDTOS
     * @param isStartProcess 是否启动审核流
     * @return
     */
    boolean saveBatch(List<ProofDTO> proofDTOS, boolean isStartProcess);


    /**
     * 单条插入
     * @param proofDTO
     * @param isStartProcess 是否启动审核流
     * @return
     */
    default boolean save(ProofDTO proofDTO, boolean isStartProcess) {
        return this.saveBatch(Arrays.asList(proofDTO), isStartProcess);
    }

    /**
     * 批量插入（默认不启动流程）
     * @param proofDTOS
     * @return
     */
    default boolean saveBatch(List<ProofDTO> proofDTOS) {
        return this.saveBatch(proofDTOS, false);
    }

    /**
     * 单条插入（默认不启动流程）
     * @param ProofDTO
     * @return
     */
    default boolean save(ProofDTO ProofDTO) {
        return this.save(ProofDTO, false);
    }

    /**
     * 批量更新
     * @param proofDTOS
     * @return
     */
    boolean updateBatch(List<ProofDTO> proofDTOS);

    /**
     * 单条更新
     * @param proofDTO
     * @return
     */
    default boolean update(ProofDTO proofDTO) {
        return this.updateBatch(Arrays.asList(proofDTO));
    }

    /**
     * 批量删除
     * @param proofIds
     * @return
     */
    boolean removeByIds(Collection<?> proofIds);

    /**
     * 单条删除
     * @param proofId
     * @return
     */
    default boolean removeById(Serializable proofId) {
        return this.removeByIds(Arrays.asList(proofId));
    }

    /**
     * 批量保存指定类型的link
     *
     * @param linkDTOS
     * @return
     */
    List<LinkDTO> saveLinkBatch(@NotEmpty List<LinkDTO> linkDTOS);

    /**
     * 批量保存指定类型的link
     *
     * @param linkDTOS
     * @param typeEnum 类型枚举
     * @return
     */
    List<LinkDTO> saveLinkBatch(@NotEmpty List<LinkDTO> linkDTOS, @NotNull TypeEnum typeEnum);

    /**
     * 单条保存指定类型的link
     *
     * @param source
     * @param target
     * @param typeEnum
     * @return
     */
    LinkDTO saveLink(@NotBlank String source, @NotBlank String target, @NotNull TypeEnum typeEnum);

    /**
     * 批量保存“包含”类型的link
     *
     * @param linkDTOS
     * @return
     */
    default List<LinkDTO> saveIncludeLinkBatch(List<LinkDTO> linkDTOS) {
        return this.saveLinkBatch(linkDTOS, TypeEnum.INCLUDE);
    }

    /**
     * 单条保存“包含”类型的link
     *
     * @param source 来源
     * @param target 目标
     * @return
     */
    default LinkDTO saveIncludeLink(String source, String target) {
        return this.saveLink(source, target, TypeEnum.INCLUDE);
    }

    /**
     * 批量保存“来源”类型的link
     *
     * @param linkDTOS
     * @return
     */
    default List<LinkDTO> saveSourceLinkBatch(List<LinkDTO> linkDTOS) {
        return this.saveLinkBatch(linkDTOS, TypeEnum.SOURCE);
    }

    /**
     * 单条保存“来源”类型的link
     *
     * @param source 来源
     * @param target 目标
     * @return
     */
    default LinkDTO saveSourceLink(String source, String target) {
        return this.saveLink(source, target, TypeEnum.SOURCE);
    }

    /**
     * 批量保存“顺序”类型的link
     *
     * @param linkDTOS
     * @return
     */
    default List<LinkDTO> saveSequenceLinkBatch(List<LinkDTO> linkDTOS) {
        return this.saveLinkBatch(linkDTOS, TypeEnum.SEQUENCE);
    }

    /**
     * 单条保存“顺序”类型的link
     *
     * @param source 来源
     * @param target 目标
     * @return
     */
    default LinkDTO saveSequenceLink(String source, String target) {
        return this.saveLink(source, target, TypeEnum.SEQUENCE);
    }

    /**
     * 批量保存“消息”类型的link
     *
     * @param linkDTOS
     * @return
     */
    default List<LinkDTO> saveMessageLinkBatch(List<LinkDTO> linkDTOS) {
        return this.saveLinkBatch(linkDTOS, TypeEnum.MESSAGE);
    }

    /**
     * 单条保存“消息”类型的link
     *
     * @param source 来源
     * @param target 目标
     * @return
     */
    default LinkDTO saveMessageLink(String source, String target) {
        return this.saveLink(source, target, TypeEnum.MESSAGE);
    }

    /**
     * 根据sources删除Link（多个source）
     * @param sources
     * @param typeEnum 指定类型
     * @return
     */
    boolean removeLinkBySources(@NotEmpty Collection<String> sources, @NotNull TypeEnum typeEnum);

    /**
     * 根据sources删除“包含”类型的Link（多个source）
     * @param sources
     * @return
     */
    default boolean removeIncludeLinkBySources(Collection<String> sources) {
        return this.removeLinkBySources(sources, TypeEnum.INCLUDE);
    }

    /**
     * 根据source删除“包含”类型的Link（单个source）
     * @param source
     * @return
     */
    default boolean removeIncludeLinkBySource(String source) {
        return this.removeLinkBySources(Arrays.asList(source), TypeEnum.INCLUDE);
    }

    /**
     * 根据sources删除“来源”类型的Link（多个source）
     * @param sources
     * @return
     */
    default boolean removeSourceLinkBySources(Collection<String> sources) {
        return this.removeLinkBySources(sources, TypeEnum.SOURCE);
    }

    /**
     * 根据source删除“来源”类型的Link（单个source）
     * @param source
     * @return
     */
    default boolean removeSourceLinkBySource(String source) {
        return this.removeLinkBySources(Arrays.asList(source), TypeEnum.SOURCE);
    }

    /**
     * 根据sources删除“顺序”类型的Link（多个source）
     * @param sources
     * @return
     */
    default boolean removeSequenceLinkBySources(Collection<String> sources) {
        return this.removeLinkBySources(sources, TypeEnum.SEQUENCE);
    }

    /**
     * 根据source删除“顺序”类型的Link（单个source）
     * @param source
     * @return
     */
    default boolean removeSequenceLinkBySource(String source) {
        return this.removeLinkBySources(Arrays.asList(source), TypeEnum.SEQUENCE);
    }

    /**
     * 根据sources删除“消息”类型的Link（多个source）
     * @param sources
     * @return
     */
    default boolean removeMessageLinkBySources(Collection<String> sources) {
        return this.removeLinkBySources(sources, TypeEnum.MESSAGE);
    }

    /**
     * 根据source删除“消息”类型的Link（单个source）
     * @param source
     * @return
     */
    default boolean removeMessageLinkBySource(String source) {
        return this.removeLinkBySources(Arrays.asList(source), TypeEnum.MESSAGE);
    }

    /**
     * 根据targets删除（多个target）
     * @param targets
     * @param typeEnum 指定类型
     * @return
     */
    boolean removeLinkByTargets(@NotEmpty Collection<String> targets, @NotNull TypeEnum typeEnum);

    /**
     * 根据targets删除“包含”类型的Link（多个target）
     * @param targets
     * @return
     */
    default boolean removeIncludeLinkByTargets(Collection<String> targets) {
        return this.removeLinkByTargets(targets, TypeEnum.INCLUDE);
    }

    /**
     * 根据target删除“包含”类型的Link（单个target）
     * @param target
     * @return
     */
    default boolean removeIncludeLinkByTarget(String target) {
        return this.removeLinkByTargets(Arrays.asList(target), TypeEnum.INCLUDE);
    }

    /**
     * 根据targets删除“来源”类型的Link（多个target）
     * @param targets
     * @return
     */
    default boolean removeSourceLinkByTargets(Collection<String> targets) {
        return this.removeLinkByTargets(targets, TypeEnum.SOURCE);
    }

    /**
     * 根据target删除“来源”类型的Link（单个target）
     * @param target
     * @return
     */
    default boolean removeSourceLinkByTarget(String target) {
        return this.removeLinkByTargets(Arrays.asList(target), TypeEnum.SOURCE);
    }

    /**
     * 根据targets删除“顺序”类型的Link（多个target）
     * @param targets
     * @return
     */
    default boolean removeSequenceLinkByTargets(Collection<String> targets) {
        return this.removeLinkByTargets(targets, TypeEnum.SEQUENCE);
    }

    /**
     * 根据target删除“顺序”类型的Link（单个target）
     * @param target
     * @return
     */
    default boolean removeSequenceLinkByTarget(String target) {
        return this.removeLinkByTargets(Arrays.asList(target), TypeEnum.SEQUENCE);
    }

    /**
     * 根据targets删除“消息”类型的Link（多个target）
     * @param targets
     * @return
     */
    default boolean removeMessageLinkByTargets(Collection<String> targets) {
        return this.removeLinkByTargets(targets, TypeEnum.MESSAGE);
    }

    /**
     * 根据target删除“消息”类型的Link（单个target）
     * @param target
     * @return
     */
    default boolean removeMessageLinkByTarget(String target) {
        return this.removeLinkByTargets(Arrays.asList(target), TypeEnum.MESSAGE);
    }

    /**
     *proof完成数量和状态反写
     * @param dtos
     * @return
     */
    List<Proof> updateStateAndQuantity(List<StateUpdateDTO> dtos);
}
