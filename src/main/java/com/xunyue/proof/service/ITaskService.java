package com.xunyue.proof.service;

import com.xunyue.proof.entity.Task;
import com.xunyue.base.XYBaseService;
import com.xunyue.proof.entity.model.dto.TaskDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.*;
import cn.hutool.core.collection.CollectionUtil;

/**
 * @apiNote  服务类
 * <AUTHOR>
 * @since 2025-01-23
 */
public interface ITaskService extends XYBaseService<Task , TaskDTO> {

    /**
     * 批量保存或更新
     * @param dtoList 信息的DTO对象列表，包含需要保存或更新的数据
     * @return 保存或更新后的信息DTO对象列表，如果保存或更新失败则返回一个空数组
     */
    List<TaskDTO> saveOrUpdateBatch(List<TaskDTO> dtoList);

    /**
     * 保存或更新
     *
     * @param dto 信息的DTO对象，包含需要保存或更新的数据
     * @return 保存或更新后的信息DTO对象，如果无结果则返回null
     */
    default TaskDTO saveOrUpdate(TaskDTO dto) {
        List<TaskDTO> resultList = this.saveOrUpdateBatch(Collections.singletonList(dto));
        return CollectionUtil.isEmpty(resultList) ? null : resultList.get(0);
    }

    /**
     * 批量保存
     * @param dtoList 信息的DTO对象列表，包含需要保存的数据
     * @return 保存后的信息DTO对象列表，如果保存失败则返回一个空数组
     */
    List<TaskDTO> saveBatch(List<TaskDTO> dtoList);

    /**
     * 保存
     *
     * @param dto DTO对象，包含的相关信息
     * @return 保存后的DTO对象，如果保存失败则返回null
     */
    default TaskDTO save(TaskDTO dto) {
        List<TaskDTO> resultList = this.saveBatch(Collections.singletonList(dto));
        return CollectionUtil.isEmpty(resultList) ? null : resultList.get(0);
    }

    /**
     * 更新
     * @param dtoList 信息的DTO对象列表，包含需要更新的数据
     * @return 更新后的信息DTO对象列表，如果更新失败则返回一个空数组
     */
    List<TaskDTO> updateBatch(List<TaskDTO> dtoList);

    /**
     * 更新
     *
     * @param dto dto
     * @return 更新后的DTO对象如果更新操作失败或没有返回结果，则返回null
     */
    default TaskDTO update(TaskDTO dto) {
        List<TaskDTO> resultList = this.updateBatch(Collections.singletonList(dto));
        return CollectionUtil.isEmpty(resultList) ? null : resultList.get(0);
    }

   /**
    * 根据id删除
    * @param id id
    * @return 删除结果
    */
    default Boolean removeById(String id){
        return this.removeBatch(Collections.singletonList(id));
    };

    /**
     * 批量删除
     * @param ids id集合
     * @return 删除结果
     */
    Boolean removeBatch(Collection<String> ids);

}
