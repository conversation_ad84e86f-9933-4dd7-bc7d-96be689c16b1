package com.xunyue.proof.service.impl;

import com.xunyue.proof.entity.Task;
import com.xunyue.proof.mapper.TaskMapper;
import com.xunyue.proof.service.ITaskService;
import com.xunyue.base.XYBaseServiceImpl;
import com.xunyue.proof.entity.model.dto.TaskDTO;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.xunyue.node.support.interfaces.NodeClazzService;
import com.xunyue.common.util.XyBeanUtil;
import com.xunyue.common.util.wrapper.JoinWrapperUtil;
import org.springframework.stereotype.Service;
import java.util.Collection;
import com.xunyue.node.common.enums.ClazzEnum;
import java.util.List;
import lombok.RequiredArgsConstructor;
import java.util.Collections;
import com.xunyue.proof.entity.Proof;
import com.xunyue.proof.entity.model.dto.ProofDTO;
import com.xunyue.proof.service.IProofService;
import com.xunyue.node.service.INodeLogService;
import cn.hutool.core.util.ObjUtil;
import java.util.stream.Collectors;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
@RequiredArgsConstructor
@Service("xyPrdTaskService")
public class TaskServiceImpl extends XYBaseServiceImpl<TaskMapper, Task , TaskDTO> implements ITaskService , NodeClazzService {

    private final IProofService proofService;

    private final INodeLogService nodeLogService;

    @Override
    public List<TaskDTO> saveOrUpdateBatch(List<TaskDTO> dtoList) {
        // 先保存（proof）
        proofService.saveOrUpdateBatch(XyBeanUtil.copyListByGst(dtoList, ProofDTO.class));
        // 在保存自己
        super.saveOrUpdateBatch(XyBeanUtil.copyListByGst(dtoList, Task.class));
        // 记录log
        nodeLogService.saveBatchByDTO(dtoList);
        return dtoList;
    }

    @Override
    public List<TaskDTO> saveBatch(List<TaskDTO> dtoList) {
        // 先保存（proof）
        proofService.saveBatch(XyBeanUtil.copyListByGst(dtoList, ProofDTO.class));
        // 在保存自己
        super.saveBatch(XyBeanUtil.copyListByGst(dtoList, Task.class));
        // 记录log
        nodeLogService.saveInsertLogBatch(dtoList);
        return dtoList;
    }

    @Override
    public List<TaskDTO> updateBatch(List<TaskDTO> dtoList) {
        // 先保存（proof）
        proofService.updateBatch(XyBeanUtil.copyListByGst(dtoList, ProofDTO.class));
        // 在保存自己
        super.updateBatchById(XyBeanUtil.copyListByGst(dtoList, Task.class));
        // 记录log
        nodeLogService.saveUpdateLogBatch(dtoList);
        return dtoList;
    }

    @Override
    public Boolean removeBatch(Collection<String> ids) {
        return proofService.removeByIds(ids) && nodeLogService.saveDeleteLogBatchByNodeIds(ids);
    }

    @Override
    public void buildSelectAllWrapper(MPJLambdaWrapper<Task> wrapper, TaskDTO dto) {
        super.buildSelectAllWrapper(wrapper, dto);
        // 再innerJoin父级（proof）
        ProofDTO proofDTO = XyBeanUtil.copyPropertiesByGst(dto, ProofDTO.class);
        proofService.innerJoin(wrapper, proofDTO, on ->
                on.apply(proofService.getAlias() + "." + proofService.getColumnPrefix() + "id" + " = " + getAlias() + ".id"));
    }

    @Override
    public MPJLambdaWrapper<Task> buildSelectAllWrapper(MPJLambdaWrapper<Task> wrapper, Collection<String> ids) {
        super.buildSelectAllWrapper(wrapper, ids);
        // 再innerJoin父级（proof）
        proofService.innerJoin(wrapper, ids, on ->
                on.apply(proofService.getAlias() + "." + proofService.getColumnPrefix() + "id" + " = " + getAlias() + ".id"));
        return wrapper;
    }

    @Override
    public void buildCondition(MPJLambdaWrapper<Task> wrapper, TaskDTO dto) {
        if (ObjUtil.isNull(dto)) {
            return;
        }
        // TODO 索引匹配逻辑需要自己实现

        //
        wrapper.eq(ObjUtil.isNotEmpty(dto.getTaskId()) , Task::getId, dto.getTaskId());
        wrapper.in(ObjUtil.isNotEmpty(dto.getTaskIds()) , Task::getId, dto.getTaskIds());

//        // 任务类型
//        wrapper.eq(ObjUtil.isNotEmpty(dto.getTaskType()) , Task::getType, dto.getTaskType());
//
//        // 任务状态
//        wrapper.eq(ObjUtil.isNotEmpty(dto.getTaskStatus()) , Task::getStatus, dto.getTaskStatus());
//
//        // 任务截止日期
//        wrapper.eq(ObjUtil.isNotEmpty(dto.getTaskDeadline()) , Task::getDeadline, dto.getTaskDeadline());

    }


    @Override
    public List<?> getNodeDTOSByNodeIds(Collection<String> ids) {
        // TODO 在此实现批量查询主子表数据接口
        return this.getByIds(ids);
    }

    @Override
    public ClazzEnum getClazz() {
        return ClazzEnum.TASK;
    }
}



