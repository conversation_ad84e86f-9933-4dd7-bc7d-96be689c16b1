package com.xunyue.proof.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.xunyue.base.XYBaseServiceImpl;
import com.xunyue.common.util.XyBeanUtil;
import com.xunyue.config.util.RedisUtil;
import com.xunyue.node.common.enums.TypeEnum;
import com.xunyue.node.entity.Link;
import com.xunyue.node.entity.model.dto.LinkDTO;
import com.xunyue.node.entity.model.dto.NodeDTO;
import com.xunyue.node.service.ILinkService;
import com.xunyue.node.service.INodeService;
import com.xunyue.node.service.impl.LinkServiceImpl;
import com.xunyue.proof.entity.Proof;
import com.xunyue.proof.entity.model.dto.ProofDTO;
import com.xunyue.proof.entity.model.dto.StateUpdateDTO;
import com.xunyue.proof.entity.model.dto.UpdateStorageStateAndQuantityDTO;
import com.xunyue.proof.mapper.ProofMapper;
import com.xunyue.proof.service.IProofService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 元数据 凭证 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ProofServiceImpl extends XYBaseServiceImpl<ProofMapper, Proof, ProofDTO> implements IProofService {

    private final INodeService nodeService;
    private final ILinkService linkService;
    private final RedisUtil redisUtil;

    @Override
    public void buildSelectAllWrapper(MPJLambdaWrapper<Proof> wrapper, ProofDTO proofDTO) {
        super.buildSelectAllWrapper(wrapper, proofDTO);
        // 再innerJoin父级（node）
        NodeDTO nodeDTO = XyBeanUtil.copyPropertiesByGst(proofDTO, NodeDTO.class);
        nodeService.innerJoin(wrapper, nodeDTO, on -> on
                .apply(nodeService.getAlias() + Constants.DOT + "node_id" + Constants.EQUALS + this.getAlias() + Constants.DOT + "id"));
    }

    @Override
    public MPJLambdaWrapper<Proof> buildSelectAllWrapper(MPJLambdaWrapper<Proof> wrapper, Collection<String> ids) {
        super.buildSelectAllWrapper(wrapper, ids);
        // 再innerJoin父级（node）
        nodeService.innerJoin(wrapper, ids, on -> on
                .apply(nodeService.getAlias() + Constants.DOT + "node_id" + Constants.EQUALS + this.getAlias() + Constants.DOT + "id"));
        return wrapper;
    }


    @Override
    public void buildCondition(MPJLambdaWrapper<Proof> wrapper, ProofDTO dto) {
        if (ObjUtil.isNull(dto)) {
            return;
        }
        // TODO 索引匹配逻辑需要自己实现

        // PK
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofId()) , Proof::getId, dto.getProofId());
        wrapper.in(ObjUtil.isNotEmpty(dto.getProofIds()) , Proof::getId, dto.getProofIds());

        // 租户id
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofTenantId()) , Proof::getTenantId, dto.getProofTenantId());
        wrapper.in(ObjUtil.isNotEmpty(dto.getProofTenantIds()) , Proof::getTenantId, dto.getProofTenantIds());

        //
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofSource()) , Proof::getSource, dto.getProofSource());

        //
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofTarget()) , Proof::getTarget, dto.getProofTarget());

        // 开始时间
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofBeginTime()) , Proof::getBeginTime, dto.getProofBeginTime());

        // 结束时间
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofEndTime()) , Proof::getEndTime, dto.getProofEndTime());

        // 打印次数
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofPrintTimes()) , Proof::getPrintTimes, dto.getProofPrintTimes());

        // 单据日期
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofReceiptDate()) , Proof::getReceiptDate, dto.getProofReceiptDate());
        wrapper.ge(ObjUtil.isNotEmpty(dto.getProofFromReceiptDate()), Proof::getReceiptDate, dto.getProofFromReceiptDate());
        wrapper.le(ObjUtil.isNotEmpty(dto.getProofToReceiptDate()), Proof::getReceiptDate, dto.getProofToReceiptDate());

        // 优先级
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofPriority()) , Proof::getPriority, dto.getProofPriority());

        // fsc声明
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofFscType()) , Proof::getFscType, dto.getProofFscType());

        // 交货日期
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofDeliveryDate()) , Proof::getDeliveryDate, dto.getProofDeliveryDate());
        wrapper.ge(ObjUtil.isNotEmpty(dto.getProofFromDeliveryDate()), Proof::getDeliveryDate, dto.getProofFromDeliveryDate());
        wrapper.le(ObjUtil.isNotEmpty(dto.getProofToDeliveryDate()), Proof::getDeliveryDate, dto.getProofToDeliveryDate());

        // 来源类型
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofSourceType()) , Proof::getSourceType, dto.getProofSourceType());

        // 物料id
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofMaterialId()) , Proof::getMaterialId, dto.getProofMaterialId());
        wrapper.in(ObjUtil.isNotEmpty(dto.getProofMaterialIds()) , Proof::getMaterialId, dto.getProofMaterialIds());

        // 单位编码
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofUnitCode()) , Proof::getUnitCode, dto.getProofUnitCode());

        // 数量
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofQuantity()) , Proof::getQuantity, dto.getProofQuantity());

        // 完成数量
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofCompleteQuantity()) , Proof::getCompleteQuantity, dto.getProofCompleteQuantity());

        // 完成状态
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofCompleteStatus()) , Proof::getCompleteStatus, dto.getProofCompleteStatus());

        // 单据类型（一个常量值，与node里的defaultTypeId不一样，推荐使用字典）
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofReceiptType()) , Proof::getReceiptType, dto.getProofReceiptType());

        // 关联任务id
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofRelatedTasksId()) , Proof::getRelatedTasksId, dto.getProofRelatedTasksId());
        wrapper.in(ObjUtil.isNotEmpty(dto.getProofRelatedTasksIds()) , Proof::getRelatedTasksId, dto.getProofRelatedTasksIds());

        // 可用日程id
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofAvailableScheduleId()) , Proof::getAvailableScheduleId, dto.getProofAvailableScheduleId());
        wrapper.in(ObjUtil.isNotEmpty(dto.getProofAvailableScheduleIds()) , Proof::getAvailableScheduleId, dto.getProofAvailableScheduleIds());

        // 计划产出数量
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofPlannedOutputQuantity()) , Proof::getPlannedOutputQuantity, dto.getProofPlannedOutputQuantity());

        // 生产班次_id
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofProductionShiftId()) , Proof::getProductionShiftId, dto.getProofProductionShiftId());
        wrapper.in(ObjUtil.isNotEmpty(dto.getProofProductionShiftIds()) , Proof::getProductionShiftId, dto.getProofProductionShiftIds());

        // 设备_id
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofEquipmentId()) , Proof::getEquipmentId, dto.getProofEquipmentId());
        wrapper.in(ObjUtil.isNotEmpty(dto.getProofEquipmentIds()) , Proof::getEquipmentId, dto.getProofEquipmentIds());

        // 工序类型_id
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofProductionProcessesTypeId()) , Proof::getProductionProcessesTypeId, dto.getProofProductionProcessesTypeId());
        wrapper.in(ObjUtil.isNotEmpty(dto.getProofProductionProcessesTypeIds()) , Proof::getProductionProcessesTypeId, dto.getProofProductionProcessesTypeIds());

        // 计划需时(小时)
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofPlannedTimeRequired()) , Proof::getPlannedTimeRequired, dto.getProofPlannedTimeRequired());

        // 最终需时
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofEndTimeRequired()) , Proof::getEndTimeRequired, dto.getProofEndTimeRequired());

        // 实际需时(小时)
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofActualTimeRequired()) , Proof::getActualTimeRequired, dto.getProofActualTimeRequired());

        // 是否手动修改计划生产时间
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofIsManualModificationDate()) , Proof::getIsManualModificationDate, dto.getProofIsManualModificationDate());

    }


    @Override
    public boolean saveOrUpdateBatch(List<ProofDTO> proofDTOS, boolean isStartProcess) {
        // 保存node
        nodeService.saveOrUpdateBatch(XyBeanUtil.copyListByGst(proofDTOS, NodeDTO.class), isStartProcess);
        // 保存proof
        return super.saveOrUpdateBatch(XyBeanUtil.copyListByGst(proofDTOS, Proof.class));
    }

    @Override
    public boolean saveBatch(List<ProofDTO> proofDTOS, boolean isStartProcess) {
        // 保存node
        nodeService.saveBatch(XyBeanUtil.copyListByGst(proofDTOS, NodeDTO.class), isStartProcess);
        // 保存proof
        return super.saveBatch(XyBeanUtil.copyListByGst(proofDTOS, Proof.class));
    }

    @Override
    public boolean updateBatch(List<ProofDTO> proofDTOS) {
        // 更新node
        nodeService.updateBatch(XyBeanUtil.copyListByGst(proofDTOS, NodeDTO.class));
        // 更新proof
        return super.updateBatchById(XyBeanUtil.copyListByGst(proofDTOS, Proof.class));
    }

    @Override
    public boolean removeByIds(Collection<?> proofIds) {
        return nodeService.removeByIds(proofIds);
    }

    @Override
    public List<LinkDTO> saveLinkBatch(List<LinkDTO> linkDTOS) {
        List<LinkDTO> resultList = linkService.saveBatch(linkDTOS);
        // 筛选包含关系的source集合
        Set<String> sourceSet = linkDTOS.stream()
                .filter(linkDTO ->
                        TypeEnum.INCLUDE.getCode().equals(linkDTO.getLinkTypeCode()) && Objects.nonNull(linkDTO.getLinkSource()))
                .map(LinkDTO::getLinkSource)
                .collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(sourceSet)) {
            // 修改proof表的is_leaf字段为false
            baseMapper.update(Wrappers.<Proof>lambdaUpdate().set(Proof::getIsLeaf, false).in(Proof::getId, sourceSet));
        }
        return resultList;
    }

    @Override
    public List<LinkDTO> saveLinkBatch(List<LinkDTO> linkDTOS, TypeEnum typeEnum) {
        linkDTOS.forEach(linkDTO -> linkDTO.setLinkTypeCode(typeEnum.getCode()));
        return this.saveLinkBatch(linkDTOS);
    }

    @Override
    public LinkDTO saveLink(String source, String target, TypeEnum typeEnum) {
        LinkDTO linkDTO = new LinkDTO(source, target, typeEnum.getCode());
        List<LinkDTO> resultList = this.saveLinkBatch(Collections.singletonList(linkDTO));
        return CollectionUtil.isEmpty(resultList) ? null : resultList.get(0);
    }

    @Override
    public boolean removeLinkBySources(Collection<String> sources, TypeEnum typeEnum) {
        boolean result = linkService.removeBySources(sources, typeEnum);
        if (result && TypeEnum.INCLUDE.equals(typeEnum)) {
            // 根据source解除包含关系的话，修改proof表的is_leaf字段为true
            baseMapper.update(Wrappers.<Proof>lambdaUpdate().set(Proof::getIsLeaf, true).in(Proof::getId, sources));
        }
        return result;
    }

    @Override
    public boolean removeLinkByTargets(Collection<String> targets, TypeEnum typeEnum) {
        Set<String> sourceSet = null;
        if (TypeEnum.INCLUDE.equals(typeEnum)) {
            // 根据target解除包含关系的话，先查询link获取source集合
            sourceSet = linkService.list(Wrappers.<Link>lambdaQuery().in(Link::getTarget, targets).eq(Link::getTypeCode, typeEnum.getCode()))
                    .stream().map(Link::getSource).filter(Objects::nonNull).collect(Collectors.toSet());
        }
        boolean result = linkService.removeByTargets(targets, typeEnum);
        if (result && CollectionUtil.isNotEmpty(sourceSet)) {
            // 查询source集合内的数据是否还存在包含关系的link，如果不存在，修改proof表的is_leaf字段为true
            Set<String> existSourceSet = linkService.list(Wrappers.<Link>lambdaQuery().in(Link::getSource, sourceSet).eq(Link::getTypeCode, typeEnum.getCode()))
                    .stream().map(Link::getSource).filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(existSourceSet)) {
                sourceSet = sourceSet.stream().filter(source -> !existSourceSet.contains(source)).collect(Collectors.toSet());
            }
            if (CollectionUtil.isNotEmpty(sourceSet)) {
                baseMapper.update(Wrappers.<Proof>lambdaUpdate().set(Proof::getIsLeaf, true).in(Proof::getId, sourceSet));
            }
        }
        return result;
    }

    /**
     * proof完成数量和状态反写
     *
     * @param dtos
     * @return
     */
    @Override
    public List<Proof> updateStateAndQuantity(List<StateUpdateDTO> dtos) {
        for (StateUpdateDTO dto : dtos) {
            this.baseMapper.update(new LambdaUpdateWrapper<Proof>().eq(Proof::getId, dto.getProofId())
                    .set(ObjUtil.isNotEmpty(dto.getState()), Proof::getCompleteStatus, dto.getState())
                    .setIncrBy(ObjUtil.isNotEmpty(dto.getQuantity()), Proof::getCompleteQuantity, dto.getQuantity()));
        }
       return this.baseMapper.selectBatchIds(
                dtos.stream().map(StateUpdateDTO::getProofId).collect(Collectors.toSet())
        );
    }
}
