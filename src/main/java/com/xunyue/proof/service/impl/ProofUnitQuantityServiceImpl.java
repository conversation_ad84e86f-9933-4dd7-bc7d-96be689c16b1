package com.xunyue.proof.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.xunyue.base.XYBaseServiceImpl;
import com.xunyue.common.util.XyBeanUtil;
import com.xunyue.node.common.enums.ClazzEnum;
import com.xunyue.node.entity.model.dto.NodeDTO;
import com.xunyue.node.service.INodeLogService;
import com.xunyue.node.service.INodeService;
import com.xunyue.node.support.interfaces.NodeClazzService;
import com.xunyue.proof.entity.ProofUnitQuantity;
import com.xunyue.proof.entity.Task;
import com.xunyue.proof.entity.model.dto.ProofDTO;
import com.xunyue.proof.entity.model.dto.ProofUnitQuantityDTO;
import com.xunyue.proof.mapper.ProofUnitQuantityMapper;
import com.xunyue.proof.service.IProofService;
import com.xunyue.proof.service.IProofUnitQuantityService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;


/**
 * <p>
 * 单据单位数量关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@RequiredArgsConstructor
@Service
public class ProofUnitQuantityServiceImpl extends XYBaseServiceImpl<ProofUnitQuantityMapper, ProofUnitQuantity , ProofUnitQuantityDTO> implements IProofUnitQuantityService , NodeClazzService {

    private final IProofService proofService;

    private final INodeLogService nodeLogService;

    @Override
    public List<ProofUnitQuantityDTO> saveOrUpdateBatch(List<ProofUnitQuantityDTO> dtoList) {
        // 先保存（proof）
        proofService.saveOrUpdateBatch(XyBeanUtil.copyListByGst(dtoList, ProofDTO.class));
        // 在保存自己
        super.saveOrUpdateBatch(XyBeanUtil.copyListByGst(dtoList, ProofUnitQuantity.class));
        // 记录log
        nodeLogService.saveBatchByDTO(dtoList);
        return dtoList;
    }

    @Override
    public List<ProofUnitQuantityDTO> saveBatch(List<ProofUnitQuantityDTO> dtoList) {
        // 先保存（proof）
        proofService.saveOrUpdateBatch(XyBeanUtil.copyListByGst(dtoList, ProofDTO.class));
        // 在保存自己
        super.saveBatch(XyBeanUtil.copyListByGst(dtoList, ProofUnitQuantity.class));
        // 记录log
        nodeLogService.saveInsertLogBatch(dtoList);
        return dtoList;
    }

    @Override
    public List<ProofUnitQuantityDTO> updateBatch(List<ProofUnitQuantityDTO> dtoList) {
        // 先保存（proof）
        proofService.saveOrUpdateBatch(XyBeanUtil.copyListByGst(dtoList, ProofDTO.class));
        // 在保存自己
        super.updateBatchById(XyBeanUtil.copyListByGst(dtoList, ProofUnitQuantity.class));
        // 记录log
        nodeLogService.saveUpdateLogBatch(dtoList);
        return dtoList;
    }

    @Override
    public Boolean removeBatch(Collection<String> ids) {
        return proofService.removeByIds(ids) && nodeLogService.saveDeleteLogBatchByNodeIds(ids);
    }

    @Override
    public void buildSelectAllWrapper(MPJLambdaWrapper<ProofUnitQuantity> wrapper, ProofUnitQuantityDTO dto) {
        super.buildSelectAllWrapper(wrapper, dto);
        // 再innerJoin父级（proof）
        ProofDTO proofDTO = XyBeanUtil.copyPropertiesByGst(dto, ProofDTO.class);
        proofService.innerJoin(wrapper, proofDTO, on ->
                on.apply(proofService.getAlias() + "." + proofService.getColumnPrefix() + "id" + " = " + getAlias() + ".id"));
    }

    @Override
    public MPJLambdaWrapper<ProofUnitQuantity> buildSelectAllWrapper(MPJLambdaWrapper<ProofUnitQuantity> wrapper, Collection<String> ids) {
        super.buildSelectAllWrapper(wrapper, ids);
        // 再innerJoin父级（proof）
        proofService.innerJoin(wrapper, ids, on ->
                on.apply(proofService.getAlias() + "." + proofService.getColumnPrefix() + "id" + " = " + getAlias() + ".id"));
        return wrapper;
    }

    @Override
    public void buildCondition(MPJLambdaWrapper<ProofUnitQuantity> wrapper, ProofUnitQuantityDTO dto) {
        if (ObjUtil.isNull(dto)) {
            return;
        }
        // TODO 索引匹配逻辑需要自己实现

        // PK
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofUnitQuantityId()) , ProofUnitQuantity::getId, dto.getProofUnitQuantityId());
        wrapper.in(ObjUtil.isNotEmpty(dto.getProofUnitQuantityIds()) , ProofUnitQuantity::getId, dto.getProofUnitQuantityIds());

        // 租户id
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofUnitQuantityTenantId()) , ProofUnitQuantity::getTenantId, dto.getProofUnitQuantityTenantId());
        wrapper.in(ObjUtil.isNotEmpty(dto.getProofUnitQuantityTenantIds()) , ProofUnitQuantity::getTenantId, dto.getProofUnitQuantityTenantIds());

        // 单位id
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofUnitQuantityUnitId()) , ProofUnitQuantity::getUnitId, dto.getProofUnitQuantityUnitId());
        wrapper.in(ObjUtil.isNotEmpty(dto.getProofUnitQuantityUnitIds()) , ProofUnitQuantity::getUnitId, dto.getProofUnitQuantityUnitIds());

        // 数量
        wrapper.eq(ObjUtil.isNotEmpty(dto.getProofUnitQuantityQuantity()) , ProofUnitQuantity::getQuantity, dto.getProofUnitQuantityQuantity());

    }


    @Override
    public List<?> getNodeDTOSByNodeIds(Collection<String> collection) {
        return getByIds(collection);
    }

    @Override
    public ClazzEnum getClazz() {
        return ClazzEnum.PROOF_UNIT_QUANTITY;
    }
}



