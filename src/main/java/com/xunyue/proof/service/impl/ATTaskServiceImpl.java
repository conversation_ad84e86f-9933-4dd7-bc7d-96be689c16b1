package com.xunyue.proof.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.github.yulichang.wrapper.JoinAbstractLambdaWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.github.yulichang.wrapper.interfaces.MConsumer;
import com.github.yulichang.wrapper.interfaces.MFunction;
import com.xunyue.base.XYBaseServiceImpl;
import com.xunyue.common.util.XyBeanUtil;
import com.xunyue.node.common.enums.ClazzEnum;
import com.xunyue.node.service.INodeLogService;
import com.xunyue.node.support.interfaces.NodeClazzService;
import com.xunyue.proof.entity.Task;
import com.xunyue.proof.entity.model.dto.ATTaskDTO;
import com.xunyue.proof.entity.model.dto.ProofDTO;
import com.xunyue.proof.entity.model.dto.TaskDTO;
import com.xunyue.proof.mapper.TaskMapper;
import com.xunyue.proof.service.IProofService;
import com.xunyue.proof.service.ITaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
@RequiredArgsConstructor
@Service("atTaskServiceImpl")
public class ATTaskServiceImpl extends XYBaseServiceImpl<TaskMapper, Task , ATTaskDTO>  {


    private final IProofService proofService;

    private final INodeLogService nodeLogService;


    @Override
    public void buildSelectAllWrapper(MPJLambdaWrapper<Task> wrapper, ATTaskDTO dto) {
        super.buildSelectAllWrapper(wrapper, dto);
//        // 再innerJoin父级（proof）
//        ProofDTO proofDTO = XyBeanUtil.copyPropertiesByGst(dto, ProofDTO.class);
//        proofService.innerJoin(wrapper, proofDTO, on ->
//                on.apply(proofService.getAlias() + "." + proofService.getColumnPrefix() + "id" + " = " + getAlias() + ".id"));
    }


}
